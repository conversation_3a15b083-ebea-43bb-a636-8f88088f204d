{"name": "psd", "description": "A general purpose Photoshop file parser.", "version": "3.3.0", "main": "./index.js", "keywords": ["psd", "parser", "photoshop", "adobe", "reader"], "repository": {"type": "git", "url": "**************:meltingice/psd.js.git"}, "dependencies": {"coffee-script": "~ 1.7.1", "coffeescript-module": "~ 0.2.1", "iconv-lite": "~ 0.4.4", "jspack": "~ 0.0.3", "lodash": "~4.17.21", "parse-engine-data": "~ 0.1", "pngjs": "3.2.0", "rsvp": "~ 3.0.6"}, "scripts": {"test": "mocha test", "docs": "node_modules/docco-husky/bin/generate lib shims", "build": "cake compile-browser", "generate-docs": "cake docs:generate", "local-dev": "npm run build && rm -rf ./examples/browser/dist && cp -R ./dist ./examples/browser/dist && webpack serve"}, "devDependencies": {"acorn": "^5.1.1", "bluebird": "^2.9.25", "browserify": "^17.0.0", "coffeeify": "~ 1.1.0", "copy-webpack-plugin": "^9.0.1", "docco-husky": "*", "html-loader": "^2.1.2", "mocha": "^9.1.1", "rimraf": "~ 2.2.8", "should": "~ 3.3.1", "uglify-js": "^2.4.21", "webpack": "^5.53.0", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.2.1"}}
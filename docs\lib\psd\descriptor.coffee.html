<!DOCTYPE html><html><head><title>descriptor.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../index.html" class="source"><span class="file_name">README</span></a><a href="../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../lib/psd/descriptor.coffee.html" class="source selected"><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>descriptor.coffee</h1><div class="filepath">lib/psd/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div><p>A descriptor is a block of data that describes a complex data structure of some kind.
It was added sometime around Photoshop 5.0 and it superceded a few legacy things such
as layer names and type data. The benefit of the Descriptor is that it is self-contained,
and allows us to dynamically define data of any size. It&#39;s always represented by an Object
at the root.</p>

</td><td class="code"><div class="highlight"><pre><span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">Descriptor</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>Creates a new Descriptor.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">constructor: </span><span class="nf">(@file) -&gt;</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>The object that will store the resulting data.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@data = </span><span class="p">{}</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>Parses the Descriptor at the current location in the file.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parse: </span><span class="nf">-&gt;</span>
    <span class="vi">@data.class = </span><span class="nx">@parseClass</span><span class="p">()</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>The descriptor defines the number of items it contains at the root.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="nv">numItems = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span></pre></div></td></tr><tr id="section-6"><td class="docs"><div class="pilwrap"><a href="#section-6" class="pilcrow">&#182;</a></div><p>Each item consists of a key/value combination, which is why our
descriptor is stored as an object instead of an array at the root.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="k">for</span> <span class="nx">i</span> <span class="k">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">...</span><span class="nx">numItems</span><span class="p">]</span>
      <span class="p">[</span><span class="nx">id</span><span class="p">,</span> <span class="nx">value</span><span class="p">]</span> <span class="o">=</span> <span class="nx">@parseKeyItem</span><span class="p">()</span>
      <span class="nx">@data</span><span class="p">[</span><span class="nx">id</span><span class="p">]</span> <span class="o">=</span> <span class="nx">value</span>

    <span class="nx">@data</span></pre></div></td></tr><tr id="section-7"><td class="docs"><div class="pilwrap"><a href="#section-7" class="pilcrow">&#182;</a></div><h2 id="note">Note</h2>
<p>The rest of the methods in this class are considered private. You will never
call any of them from outside this class.</p>

</td><td class="code"><div class="highlight"><pre></pre></div></td></tr><tr id="section-8"><td class="docs"><div class="pilwrap"><a href="#section-8" class="pilcrow">&#182;</a></div><p>Parses a class representation, which consists of a name and a unique ID.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseClass: </span><span class="nf">-&gt;</span>
    <span class="nv">name: </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUnicodeString</span><span class="p">()</span>
    <span class="nv">id: </span><span class="nx">@parseId</span><span class="p">()</span></pre></div></td></tr><tr id="section-9"><td class="docs"><div class="pilwrap"><a href="#section-9" class="pilcrow">&#182;</a></div><p>Parses an ID, which is a unique String.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseId: </span><span class="nf">-&gt;</span>
    <span class="nv">len = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="k">if</span> <span class="nx">len</span> <span class="o">is</span> <span class="mi">0</span> <span class="k">then</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span> <span class="k">else</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="nx">len</span><span class="p">)</span></pre></div></td></tr><tr id="section-10"><td class="docs"><div class="pilwrap"><a href="#section-10" class="pilcrow">&#182;</a></div><p>Parses a key/item value, which consists of an ID and an Item of any type.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseKeyItem: </span><span class="nf">-&gt;</span>
    <span class="nv">id = </span><span class="nx">@parseId</span><span class="p">()</span>
    <span class="nv">value = </span><span class="nx">@parseItem</span><span class="p">()</span>
    <span class="k">return</span> <span class="p">[</span><span class="nx">id</span><span class="p">,</span> <span class="nx">value</span><span class="p">]</span></pre></div></td></tr><tr id="section-11"><td class="docs"><div class="pilwrap"><a href="#section-11" class="pilcrow">&#182;</a></div><p>Parses an Item, which can be one of many types of data, depending on the key.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseItem: </span><span class="nf">(type = null) -&gt;</span>
    <span class="nv">type = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span> <span class="k">unless</span> <span class="nx">type</span><span class="o">?</span>

    <span class="k">switch</span> <span class="nx">type</span>
      <span class="k">when</span> <span class="s">&#39;bool&#39;</span>         <span class="k">then</span> <span class="nx">@parseBoolean</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;type&#39;</span><span class="p">,</span> <span class="s">&#39;GlbC&#39;</span> <span class="k">then</span> <span class="nx">@parseClass</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;Objc&#39;</span><span class="p">,</span> <span class="s">&#39;GlbO&#39;</span> <span class="k">then</span> <span class="k">new</span> <span class="nx">Descriptor</span><span class="p">(</span><span class="nx">@file</span><span class="p">).</span><span class="nx">parse</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;doub&#39;</span>         <span class="k">then</span> <span class="nx">@parseDouble</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;enum&#39;</span>         <span class="k">then</span> <span class="nx">@parseEnum</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;alis&#39;</span>         <span class="k">then</span> <span class="nx">@parseAlias</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;Pth&#39;</span>          <span class="k">then</span> <span class="nx">@parseFilePath</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;long&#39;</span>         <span class="k">then</span> <span class="nx">@parseInteger</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;comp&#39;</span>         <span class="k">then</span> <span class="nx">@parseLargeInteger</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;VlLs&#39;</span>         <span class="k">then</span> <span class="nx">@parseList</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;ObAr&#39;</span>         <span class="k">then</span> <span class="nx">@parseObjectArray</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;tdta&#39;</span>         <span class="k">then</span> <span class="nx">@parseRawData</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;obj &#39;</span>         <span class="k">then</span> <span class="nx">@parseReference</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;TEXT&#39;</span>         <span class="k">then</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readUnicodeString</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;UntF&#39;</span>         <span class="k">then</span> <span class="nx">@parseUnitDouble</span><span class="p">()</span>
      <span class="k">when</span> <span class="s">&#39;UnFl&#39;</span>         <span class="k">then</span> <span class="nx">@parseUnitFloat</span><span class="p">()</span>

  <span class="nv">parseBoolean: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readBoolean</span><span class="p">()</span>
  <span class="nv">parseDouble: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readDouble</span><span class="p">()</span>
  <span class="nv">parseInteger: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
  <span class="nv">parseLargeInteger: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readLongLong</span><span class="p">()</span>
  <span class="nv">parseIdentifier: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
  <span class="nv">parseIndex: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
  <span class="nv">parseOffset: </span><span class="nf">-&gt;</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span></pre></div></td></tr><tr id="section-12"><td class="docs"><div class="pilwrap"><a href="#section-12" class="pilcrow">&#182;</a></div><p>Parses a Property, which consists of a class and a unique ID.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseProperty: </span><span class="nf">-&gt;</span>
    <span class="k">class</span><span class="o">:</span> <span class="nx">@parseClass</span><span class="p">()</span>
    <span class="nv">id: </span><span class="nx">@parseId</span><span class="p">()</span></pre></div></td></tr><tr id="section-13"><td class="docs"><div class="pilwrap"><a href="#section-13" class="pilcrow">&#182;</a></div><p>Parses an enumerator, which consists of 2 IDs, one of which is
the type, and the other is the value.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseEnum: </span><span class="nf">-&gt;</span>
    <span class="nv">type: </span><span class="nx">@parseId</span><span class="p">()</span>
    <span class="nv">value: </span><span class="nx">@parseId</span><span class="p">()</span></pre></div></td></tr><tr id="section-14"><td class="docs"><div class="pilwrap"><a href="#section-14" class="pilcrow">&#182;</a></div><p>Parses an enumerator reference, which consists of a class and
2 IDs: a type and value.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseEnumReference: </span><span class="nf">-&gt;</span>
    <span class="k">class</span><span class="o">:</span> <span class="nx">@parseClass</span><span class="p">()</span>
    <span class="nv">type: </span><span class="nx">@parseId</span><span class="p">()</span>
    <span class="nv">value: </span><span class="nx">@parseId</span><span class="p">()</span></pre></div></td></tr><tr id="section-15"><td class="docs"><div class="pilwrap"><a href="#section-15" class="pilcrow">&#182;</a></div><p>Parses an Alias, which is a string of arbitrary length.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseAlias: </span><span class="nf">-&gt;</span>
    <span class="nv">len = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="nx">len</span><span class="p">)</span></pre></div></td></tr><tr id="section-16"><td class="docs"><div class="pilwrap"><a href="#section-16" class="pilcrow">&#182;</a></div><p>Parses a file path, which consists of a 4 character signature
and a path.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseFilePath: </span><span class="nf">-&gt;</span>
    <span class="nv">len = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="nv">sig = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span></pre></div></td></tr><tr id="section-17"><td class="docs"><div class="pilwrap"><a href="#section-17" class="pilcrow">&#182;</a></div><p>Little endian. Who knows.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="nv">pathSize = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">read</span><span class="p">(</span><span class="s">&#39;&lt;i&#39;</span><span class="p">)</span>
    <span class="nv">numChars = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">read</span><span class="p">(</span><span class="s">&#39;&lt;i&#39;</span><span class="p">)</span>

    <span class="nv">path = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUnicodeString</span><span class="p">(</span><span class="nx">numChars</span><span class="p">)</span>

    <span class="nv">sig: </span><span class="nx">sig</span>
    <span class="nv">path: </span><span class="nx">path</span></pre></div></td></tr><tr id="section-18"><td class="docs"><div class="pilwrap"><a href="#section-18" class="pilcrow">&#182;</a></div><p>Parses a list/array of Items.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseList: </span><span class="nf">-&gt;</span>
    <span class="nv">count = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="nv">items = </span><span class="p">[]</span>

    <span class="k">for</span> <span class="nx">i</span> <span class="k">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">...</span><span class="nx">count</span><span class="p">]</span>
      <span class="nx">items</span><span class="p">.</span><span class="nx">push</span> <span class="nx">@parseItem</span><span class="p">()</span>

    <span class="nx">items</span></pre></div></td></tr><tr id="section-19"><td class="docs"><div class="pilwrap"><a href="#section-19" class="pilcrow">&#182;</a></div><p>Not documented anywhere and unsure of the data format. Luckily, this
type is extremely rare. In fact, it&#39;s so rare, that I&#39;ve never run into it
among any of my PSDs.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseObjectArray: </span><span class="nf">-&gt;</span>
    <span class="k">throw</span> <span class="s">&quot;Descriptor object array not implemented yet @ </span><span class="si">#{</span><span class="nx">@file</span><span class="p">.</span><span class="nx">tell</span><span class="p">()</span><span class="si">}</span><span class="s">&quot;</span></pre></div></td></tr><tr id="section-20"><td class="docs"><div class="pilwrap"><a href="#section-20" class="pilcrow">&#182;</a></div><p>Parses raw byte data of arbitrary length.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseRawData: </span><span class="nf">-&gt;</span>
    <span class="nv">len = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="nx">@file</span><span class="p">.</span><span class="nx">read</span><span class="p">(</span><span class="nx">len</span><span class="p">)</span></pre></div></td></tr><tr id="section-21"><td class="docs"><div class="pilwrap"><a href="#section-21" class="pilcrow">&#182;</a></div><p>Parses a Reference, which is an array of items of multiple types.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseReference: </span><span class="nf">-&gt;</span>
    <span class="nv">numItems = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
    <span class="nv">items = </span><span class="p">[]</span>

    <span class="k">for</span> <span class="nx">i</span> <span class="k">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">...</span><span class="nx">numItems</span><span class="p">]</span>
      <span class="nv">type = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
      <span class="nv">value = </span><span class="k">switch</span> <span class="nx">type</span>
        <span class="k">when</span> <span class="s">&#39;prop&#39;</span> <span class="k">then</span> <span class="nx">@parseProperty</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;Clss&#39;</span> <span class="k">then</span> <span class="nx">@parseClass</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;Enmr&#39;</span> <span class="k">then</span> <span class="nx">@parseEnumReference</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;Idnt&#39;</span> <span class="k">then</span> <span class="nx">@parseIdentifier</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;indx&#39;</span> <span class="k">then</span> <span class="nx">@parseIndex</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;name&#39;</span> <span class="k">then</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readUnicodeString</span><span class="p">()</span>
        <span class="k">when</span> <span class="s">&#39;rele&#39;</span> <span class="k">then</span> <span class="nx">@parseOffset</span><span class="p">()</span>

      <span class="nx">items</span><span class="p">.</span><span class="nx">push</span> <span class="nv">type: </span><span class="nx">type</span><span class="p">,</span> <span class="nv">value: </span><span class="nx">value</span>

    <span class="nx">items</span></pre></div></td></tr><tr id="section-22"><td class="docs"><div class="pilwrap"><a href="#section-22" class="pilcrow">&#182;</a></div><p>Parses a double with a unit, such as angle, percent, pixels, etc.
Returns an object with an ID, a unit, and a value.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseUnitDouble: </span><span class="nf">-&gt;</span>
    <span class="nv">unitId = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
    <span class="nv">unit = </span><span class="k">switch</span> <span class="nx">unitId</span>
      <span class="k">when</span> <span class="s">&#39;#Ang&#39;</span> <span class="k">then</span> <span class="s">&#39;Angle&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Rsl&#39;</span> <span class="k">then</span> <span class="s">&#39;Density&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Rlt&#39;</span> <span class="k">then</span> <span class="s">&#39;Distance&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Nne&#39;</span> <span class="k">then</span> <span class="s">&#39;None&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Prc&#39;</span> <span class="k">then</span> <span class="s">&#39;Percent&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Pxl&#39;</span> <span class="k">then</span> <span class="s">&#39;Pixels&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Mlm&#39;</span> <span class="k">then</span> <span class="s">&#39;Millimeters&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Pnt&#39;</span> <span class="k">then</span> <span class="s">&#39;Points&#39;</span>

    <span class="nv">value = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readDouble</span><span class="p">()</span>
    <span class="nv">id: </span><span class="nx">unitId</span><span class="p">,</span> <span class="nv">unit: </span><span class="nx">unit</span><span class="p">,</span> <span class="nv">value: </span><span class="nx">value</span></pre></div></td></tr><tr id="section-23"><td class="docs"><div class="pilwrap"><a href="#section-23" class="pilcrow">&#182;</a></div><p>Parses a float with a unit, such as angle, percent, pixels, etc.
Returns an object with an ID, a unit, and a value.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseUnitFloat: </span><span class="nf">-&gt;</span>
    <span class="nv">unitId = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
    <span class="nv">unit = </span><span class="k">switch</span> <span class="nx">unitId</span>
      <span class="k">when</span> <span class="s">&#39;#Ang&#39;</span> <span class="k">then</span> <span class="s">&#39;Angle&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Rsl&#39;</span> <span class="k">then</span> <span class="s">&#39;Density&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Rlt&#39;</span> <span class="k">then</span> <span class="s">&#39;Distance&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Nne&#39;</span> <span class="k">then</span> <span class="s">&#39;None&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Prc&#39;</span> <span class="k">then</span> <span class="s">&#39;Percent&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Pxl&#39;</span> <span class="k">then</span> <span class="s">&#39;Pixels&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Mlm&#39;</span> <span class="k">then</span> <span class="s">&#39;Millimeters&#39;</span>
      <span class="k">when</span> <span class="s">&#39;#Pnt&#39;</span> <span class="k">then</span> <span class="s">&#39;Points&#39;</span>

    <span class="nv">value = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readFloat</span><span class="p">()</span>
    <span class="nv">id: </span><span class="nx">unitId</span><span class="p">,</span> <span class="nv">unit: </span><span class="nx">unit</span><span class="p">,</span> <span class="nv">value: </span><span class="nx">value</span>
    

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:10 GMT-0400 (EDT)  </div></div></body></html>
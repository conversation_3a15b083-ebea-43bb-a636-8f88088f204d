<!DOCTYPE html><html><head><title>info.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../../index.html" class="source"><span class="file_name">README</span></a><a href="../../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../../lib/psd/layer/info.coffee.html" class="source selected"><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>info.coffee</h1><div class="filepath">lib/psd/layer/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="nv">LazyExecute = </span><span class="nx">require</span> <span class="s">&#39;../lazy_execute.coffee&#39;</span>
<span class="nv">Util = </span><span class="nx">require</span> <span class="s">&#39;../util.coffee&#39;</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>This is an incredibly important object because the majority of the layer information
is contained in layer info blocks. The keys of this object define how the layer info
can be accessed. Each layer info block contains different data, so accessing the data 
within each differs from type to type.</p>
<p>Here&#39;s an example of how to access some of this data:</p>
<pre><code class="lang-coffeescript">node = psd.tree().childrenAtPath(&#39;path/to/layer&#39;)[0]
node.get(&#39;locked&#39;).allLocked
node.get(&#39;metadata&#39;).data.layerComp
node.get(&#39;typeTool&#39;).export()
</code></pre>

</td><td class="code"><div class="highlight"><pre><span class="nv">LAYER_INFO = </span><span class="p">{</span>
  <span class="nv">blendClippingElements: </span> <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/blend_clipping_elements.coffee&#39;</span><span class="p">)</span>
  <span class="nv">blendInteriorElements: </span> <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/blend_interior_elements.coffee&#39;</span><span class="p">)</span>
  <span class="nv">fillOpacity: </span>           <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/fill_opacity.coffee&#39;</span><span class="p">)</span>
  <span class="nv">gradientFill: </span>          <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/gradient_fill.coffee&#39;</span><span class="p">)</span>
  <span class="nv">layerId: </span>               <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/layer_id.coffee&#39;</span><span class="p">)</span>
  <span class="nv">layerNameSource: </span>       <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/layer_name_source.coffee&#39;</span><span class="p">)</span>
  <span class="nv">legacyTypetool: </span>        <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/legacy_typetool.coffee&#39;</span><span class="p">)</span>
  <span class="nv">locked: </span>                <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/locked.coffee&#39;</span><span class="p">)</span>
  <span class="nv">metadata: </span>              <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/metadata.coffee&#39;</span><span class="p">)</span>
  <span class="nv">name: </span>                  <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/unicode_name.coffee&#39;</span><span class="p">)</span>
  <span class="nv">nestedSectionDivider: </span>  <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/nested_section_divider.coffee&#39;</span><span class="p">)</span>
  <span class="nv">objectEffects: </span>         <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/object_effects.coffee&#39;</span><span class="p">)</span>
  <span class="nv">sectionDivider: </span>        <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/section_divider.coffee&#39;</span><span class="p">)</span>
  <span class="nv">solidColor: </span>            <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/solid_color.coffee&#39;</span><span class="p">)</span>
  <span class="nv">typeTool: </span>              <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/typetool.coffee&#39;</span><span class="p">)</span>
  <span class="nv">vectorMask: </span>            <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/vector_mask.coffee&#39;</span><span class="p">)</span>
  <span class="nv">vectorOrigination: </span>     <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/vector_origination.coffee&#39;</span><span class="p">)</span>
  <span class="nv">vectorStroke: </span>          <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/vector_stroke.coffee&#39;</span><span class="p">)</span>
  <span class="nv">vectorStrokeContent: </span>   <span class="nx">require</span><span class="p">(</span><span class="s">&#39;../layer_info/vector_stroke_content.coffee&#39;</span><span class="p">)</span>
<span class="p">}</span>

<span class="nv">module.exports =</span>
  <span class="nv">parseLayerInfo: </span><span class="nf">-&gt;</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>Layer info blocks are the last section in the layer, so we can continue until our
file cursor reaches the end of the layer.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="k">while</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">tell</span><span class="p">()</span> <span class="o">&lt;</span> <span class="nx">@layerEnd</span>
      <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="mi">4</span><span class="p">,</span> <span class="kc">true</span> <span class="c1"># sig</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>Every layer info block is identified by a unique 4 character string.</p>

</td><td class="code"><div class="highlight"><pre>      <span class="nv">key = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
      <span class="nv">length = </span><span class="nx">Util</span><span class="p">.</span><span class="nx">pad2</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>
      <span class="nv">pos = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">tell</span><span class="p">()</span>

      <span class="nv">keyParseable = </span><span class="kc">false</span>
      <span class="k">for</span> <span class="k">own</span> <span class="nx">name</span><span class="p">,</span> <span class="nx">klass</span> <span class="k">of</span> <span class="nx">LAYER_INFO</span>
        <span class="k">continue</span> <span class="k">unless</span> <span class="nx">klass</span><span class="p">.</span><span class="nx">shouldParse</span><span class="p">(</span><span class="nx">key</span><span class="p">)</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>Once we find the right class to handle the layer info block, we create it and
register it with LazyExecute. This allows us to parse the PSD significantly
faster because we don&#39;t bother parsing the layer info block until it&#39;s accessed.</p>

</td><td class="code"><div class="highlight"><pre>        <span class="nv">i = </span><span class="k">new</span> <span class="nx">klass</span><span class="p">(</span><span class="nx">@</span><span class="p">,</span> <span class="nx">length</span><span class="p">)</span>
        <span class="nx">@adjustments</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">LazyExecute</span><span class="p">(</span><span class="nx">i</span><span class="p">,</span> <span class="nx">@file</span><span class="p">)</span>
          <span class="p">.</span><span class="nx">now</span><span class="p">(</span><span class="s">&#39;skip&#39;</span><span class="p">)</span>
          <span class="p">.</span><span class="nx">later</span><span class="p">(</span><span class="s">&#39;parse&#39;</span><span class="p">)</span>
          <span class="p">.</span><span class="nx">get</span><span class="p">()</span></pre></div></td></tr><tr id="section-6"><td class="docs"><div class="pilwrap"><a href="#section-6" class="pilcrow">&#182;</a></div><p>We create a function that lets us easily access the data.</p>

</td><td class="code"><div class="highlight"><pre>        <span class="k">unless</span> <span class="nx">@</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span><span class="o">?</span>
          <span class="nx">do</span> <span class="nf">(name) =&gt;</span> <span class="nx">@</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span> <span class="o">=</span> <span class="nf">=&gt;</span> <span class="nx">@adjustments</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span></pre></div></td></tr><tr id="section-7"><td class="docs"><div class="pilwrap"><a href="#section-7" class="pilcrow">&#182;</a></div><p>For debugging purposes, we store every key that we can parse.</p>

</td><td class="code"><div class="highlight"><pre>        <span class="nx">@infoKeys</span><span class="p">.</span><span class="nx">push</span> <span class="nx">key</span>
        <span class="nv">keyParseable = </span><span class="kc">true</span>
        <span class="k">break</span></pre></div></td></tr><tr id="section-8"><td class="docs"><div class="pilwrap"><a href="#section-8" class="pilcrow">&#182;</a></div><p>If we don&#39;t know how to parse this particular layer info block, we can skip it since we
know the end position of the data.</p>

</td><td class="code"><div class="highlight"><pre>      <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="nx">length</span><span class="p">,</span> <span class="kc">true</span> <span class="k">if</span> <span class="o">not</span> <span class="nx">keyParseable</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:12 GMT-0400 (EDT)  </div></div></body></html>
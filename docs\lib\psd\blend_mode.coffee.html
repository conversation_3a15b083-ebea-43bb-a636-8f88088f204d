<!DOCTYPE html><html><head><title>blend_mode.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../index.html" class="source"><span class="file_name">README</span></a><a href="../../lib/psd/blend_mode.coffee.html" class="source selected"><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>blend_mode.coffee</h1><div class="filepath">lib/psd/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="p">{</span><span class="nx">Module</span><span class="p">}</span> <span class="o">=</span> <span class="nx">require</span> <span class="s">&#39;coffeescript-module&#39;</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>The blend mode describes important data regarding a layer, such as
the blending mode, the opacity, and whether it&#39;s a part of a clipping mask.</p>

</td><td class="code"><div class="highlight"><pre><span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">BlendMode</span> <span class="k">extends</span> <span class="nx">Module</span>
  <span class="nx">@aliasProperty</span> <span class="s">&#39;blendingMode&#39;</span><span class="p">,</span> <span class="s">&#39;mode&#39;</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>All of the blend modes are stored in the PSD file with a specific key.
This is the mapping of that key to its readable name.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">BLEND_MODES = </span><span class="p">{</span>
    <span class="nv">norm: </span><span class="s">&#39;normal&#39;</span><span class="p">,</span>
    <span class="nv">dark: </span><span class="s">&#39;darken&#39;</span><span class="p">,</span>
    <span class="nv">lite: </span><span class="s">&#39;lighten&#39;</span><span class="p">,</span>
    <span class="nv">hue: </span> <span class="s">&#39;hue&#39;</span><span class="p">,</span>
    <span class="nv">sat: </span> <span class="s">&#39;saturation&#39;</span><span class="p">,</span>
    <span class="nv">colr: </span><span class="s">&#39;color&#39;</span><span class="p">,</span>
    <span class="nv">lum: </span> <span class="s">&#39;luminosity&#39;</span><span class="p">,</span>
    <span class="nv">mul: </span> <span class="s">&#39;multiply&#39;</span><span class="p">,</span>
    <span class="nv">scrn: </span><span class="s">&#39;screen&#39;</span><span class="p">,</span>
    <span class="nv">diss: </span><span class="s">&#39;dissolve&#39;</span><span class="p">,</span>
    <span class="nv">over: </span><span class="s">&#39;overlay&#39;</span><span class="p">,</span>
    <span class="nv">hLit: </span><span class="s">&#39;hard_light&#39;</span><span class="p">,</span>
    <span class="nv">sLit: </span><span class="s">&#39;soft_light&#39;</span><span class="p">,</span>
    <span class="nv">diff: </span><span class="s">&#39;difference&#39;</span><span class="p">,</span>
    <span class="nv">smud: </span><span class="s">&#39;exclusion&#39;</span><span class="p">,</span>
    <span class="nv">div: </span> <span class="s">&#39;color_dodge&#39;</span><span class="p">,</span>
    <span class="nv">idiv: </span><span class="s">&#39;color_burn&#39;</span><span class="p">,</span>
    <span class="nv">lbrn: </span><span class="s">&#39;linear_burn&#39;</span><span class="p">,</span>
    <span class="nv">lddg: </span><span class="s">&#39;linear_dodge&#39;</span><span class="p">,</span>
    <span class="nv">vLit: </span><span class="s">&#39;vivid_light&#39;</span><span class="p">,</span>
    <span class="nv">lLit: </span><span class="s">&#39;linear_light&#39;</span><span class="p">,</span>
    <span class="nv">pLit: </span><span class="s">&#39;pin_light&#39;</span><span class="p">,</span>
    <span class="nv">hMix: </span><span class="s">&#39;hard_mix&#39;</span><span class="p">,</span>
    <span class="nv">pass: </span><span class="s">&#39;passthru&#39;</span><span class="p">,</span>
    <span class="nv">dkCl: </span><span class="s">&#39;darker_color&#39;</span><span class="p">,</span>
    <span class="nv">lgCl: </span><span class="s">&#39;lighter_color&#39;</span><span class="p">,</span>
    <span class="nv">fsub: </span><span class="s">&#39;subtract&#39;</span><span class="p">,</span>
    <span class="nv">fdiv: </span><span class="s">&#39;divide&#39;</span>
  <span class="p">}</span>

  <span class="nv">constructor: </span><span class="nf">(@file) -&gt;</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>The 4 character key for the blending mode.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@blendKey = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>The opacity of the layer, from [0, 255].</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@opacity = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-6"><td class="docs"><div class="pilwrap"><a href="#section-6" class="pilcrow">&#182;</a></div><p>Raw value for the clipping state of this layer.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@clipping = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-7"><td class="docs"><div class="pilwrap"><a href="#section-7" class="pilcrow">&#182;</a></div><p>Is this layer a clipping mask?</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@clipped = </span><span class="kc">null</span>
    <span class="vi">@flags = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-8"><td class="docs"><div class="pilwrap"><a href="#section-8" class="pilcrow">&#182;</a></div><p>The readable representation of the blend mode.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@mode = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-9"><td class="docs"><div class="pilwrap"><a href="#section-9" class="pilcrow">&#182;</a></div><p>Is this layer visible?</p>

</td><td class="code"><div class="highlight"><pre>    <span class="vi">@visible = </span><span class="kc">null</span></pre></div></td></tr><tr id="section-10"><td class="docs"><div class="pilwrap"><a href="#section-10" class="pilcrow">&#182;</a></div><p>Parses the blend mode data.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parse: </span><span class="nf">-&gt;</span>
    <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="mi">4</span><span class="p">,</span> <span class="kc">true</span>

    <span class="vi">@blendKey = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">).</span><span class="nx">trim</span><span class="p">()</span>
    <span class="vi">@opacity = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readByte</span><span class="p">()</span>
    <span class="vi">@clipping = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readByte</span><span class="p">()</span>
    <span class="vi">@flags = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readByte</span><span class="p">()</span>

    <span class="vi">@mode = </span><span class="nx">BLEND_MODES</span><span class="p">[</span><span class="nx">@blendKey</span><span class="p">]</span>
    <span class="vi">@clipped = </span><span class="nx">@clipping</span> <span class="o">is</span> <span class="mi">1</span>

    <span class="vi">@visible = </span><span class="o">!</span><span class="p">((</span><span class="nx">@flags</span> <span class="o">&amp;</span> <span class="p">(</span><span class="mh">0x01</span> <span class="o">&lt;&lt;</span> <span class="mi">1</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">)</span>

    <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="mi">1</span><span class="p">,</span> <span class="kc">true</span></pre></div></td></tr><tr id="section-11"><td class="docs"><div class="pilwrap"><a href="#section-11" class="pilcrow">&#182;</a></div><p>Returns the layer opacity as a percentage.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">opacityPercentage: </span><span class="nf">-&gt;</span> <span class="nx">@opacity</span> <span class="o">*</span> <span class="mi">100</span> <span class="o">/</span> <span class="mi">255</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:10 GMT-0400 (EDT)  </div></div></body></html>
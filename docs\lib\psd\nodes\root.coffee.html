<!DOCTYPE html><html><head><title>root.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../../index.html" class="source"><span class="file_name">README</span></a><a href="../../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/nodes/root.coffee.html" class="source selected"><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>root.coffee</h1><div class="filepath">lib/psd/nodes/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="nv">_     = </span><span class="nx">require</span> <span class="s">&#39;lodash&#39;</span>
<span class="nv">Node  = </span><span class="nx">require</span> <span class="s">&#39;../node.coffee&#39;</span>
<span class="nv">Group = </span><span class="nx">require</span> <span class="s">&#39;./group.coffee&#39;</span>
<span class="nv">Layer = </span><span class="nx">require</span> <span class="s">&#39;./layer.coffee&#39;</span>

<span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">Root</span> <span class="k">extends</span> <span class="nx">Node</span>
  <span class="vi">@layerForPsd: </span><span class="nf">(psd) -&gt;</span>
    <span class="nv">layer = </span><span class="p">{}</span>
    <span class="nx">layer</span><span class="p">[</span><span class="nx">prop</span><span class="p">]</span> <span class="o">=</span> <span class="kc">null</span> <span class="k">for</span> <span class="nx">prop</span> <span class="k">in</span> <span class="nx">Node</span><span class="p">.</span><span class="nx">PROPERTIES</span>
    
    <span class="nv">layer.top = </span><span class="mi">0</span>
    <span class="nv">layer.left = </span><span class="mi">0</span>
    <span class="nv">layer.right = </span><span class="nx">psd</span><span class="p">.</span><span class="nx">header</span><span class="p">.</span><span class="nx">width</span>
    <span class="nv">layer.bottom = </span><span class="nx">psd</span><span class="p">.</span><span class="nx">header</span><span class="p">.</span><span class="nx">height</span>
    <span class="nx">layer</span>

  <span class="nv">type: </span><span class="s">&#39;root&#39;</span>

  <span class="nv">constructor: </span><span class="nf">(@psd) -&gt;</span>
    <span class="k">super</span> <span class="nx">Root</span><span class="p">.</span><span class="nx">layerForPsd</span><span class="p">(</span><span class="nx">@psd</span><span class="p">)</span>
    <span class="nx">@buildHeirarchy</span><span class="p">()</span>

  <span class="nv">documentDimensions: </span><span class="nf">-&gt;</span> <span class="p">[</span>
    <span class="nx">@width</span><span class="p">,</span>
    <span class="nx">@height</span>
  <span class="p">]</span>

  <span class="nv">depth: </span><span class="nf">-&gt;</span> <span class="mi">0</span>
  <span class="nv">opacity: </span><span class="nf">-&gt;</span> <span class="mi">255</span>
  <span class="nv">fillOpacity: </span><span class="nf">-&gt;</span> <span class="mi">255</span>

  <span class="nv">export: </span><span class="nf">-&gt;</span>
    <span class="nv">children: </span><span class="nx">@_children</span><span class="p">.</span><span class="nx">map</span><span class="p">(</span><span class="nf">(c) -&gt;</span> <span class="nx">c</span><span class="p">.</span><span class="nx">export</span><span class="p">())</span>
    <span class="nb">document</span><span class="o">:</span>
      <span class="nv">width: </span><span class="nx">@width</span>
      <span class="nv">height: </span><span class="nx">@height</span>
      <span class="nv">resources:</span>
        <span class="nv">layerComps: </span><span class="nx">@psd</span><span class="p">.</span><span class="nx">resources</span><span class="p">.</span><span class="nx">resource</span><span class="p">(</span><span class="s">&#39;layerComps&#39;</span><span class="p">)</span><span class="o">?</span><span class="p">.</span><span class="nx">export</span><span class="p">()</span> <span class="o">or</span> <span class="p">[]</span>
        <span class="nv">guides: </span><span class="p">[]</span>
        <span class="nv">slices: </span><span class="p">[]</span>


  <span class="nv">buildHeirarchy: </span><span class="nf">-&gt;</span>
    <span class="nv">currentGroup = </span><span class="nx">@</span>
    <span class="nv">parseStack = </span><span class="p">[]</span>

    <span class="k">for</span> <span class="nx">layer</span> <span class="k">in</span> <span class="nx">@psd</span><span class="p">.</span><span class="nx">layers</span>
      <span class="k">if</span> <span class="nx">layer</span><span class="p">.</span><span class="nx">isFolder</span><span class="p">()</span>
        <span class="nx">parseStack</span><span class="p">.</span><span class="nx">push</span> <span class="nx">currentGroup</span>
        <span class="nv">currentGroup = </span><span class="k">new</span> <span class="nx">Group</span><span class="p">(</span><span class="nx">layer</span><span class="p">,</span> <span class="nx">_</span><span class="p">.</span><span class="nx">last</span><span class="p">(</span><span class="nx">parseStack</span><span class="p">))</span>
      <span class="k">else</span> <span class="k">if</span> <span class="nx">layer</span><span class="p">.</span><span class="nx">isFolderEnd</span><span class="p">()</span>
        <span class="nv">parent = </span><span class="nx">parseStack</span><span class="p">.</span><span class="nx">pop</span><span class="p">()</span>
        <span class="nx">parent</span><span class="p">.</span><span class="nx">children</span><span class="p">().</span><span class="nx">push</span> <span class="nx">currentGroup</span>
        <span class="nv">currentGroup = </span><span class="nx">parent</span>
      <span class="k">else</span>
        <span class="nx">currentGroup</span><span class="p">.</span><span class="nx">children</span><span class="p">().</span><span class="nx">push</span> <span class="k">new</span> <span class="nx">Layer</span><span class="p">(</span><span class="nx">layer</span><span class="p">,</span> <span class="nx">currentGroup</span><span class="p">)</span>

    <span class="nx">@updateDimensions</span><span class="p">()</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:14 GMT-0400 (EDT)  </div></div></body></html>
<!DOCTYPE html><html><head><title>psd.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../index.html" class="source"><span class="file_name">README</span></a><a href="../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../lib/psd.coffee.html" class="source selected"><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>psd.coffee</h1><div class="filepath">lib/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div><p>A general purpose parser for Photoshop files. PSDs are broken up in to 4 logical sections:
the header, resources, the layer mask (including layers), and the preview image. We parse
each of these sections in order.</p>
<h2 id="nodejs-examples">NodeJS Examples</h2>
<p><strong> Parsing asynchronously </strong></p>
<pre><code class="lang-coffeescript">PSD.open(&#39;path/to/file.psd&#39;).then (psd) -&gt;
  console.log psd.tree().export()
</code></pre>
<p><strong> Parsing synchronously </strong></p>
<pre><code class="lang-coffeescript">psd = PSD.fromFile(&#39;path/to/file.psd&#39;)
psd.parse()
console.log psd.tree().export()
</code></pre>

</td><td class="code"><div class="highlight"><pre><span class="nv">RSVP = </span><span class="nx">require</span> <span class="s">&#39;rsvp&#39;</span>
<span class="p">{</span><span class="nx">Module</span><span class="p">}</span> <span class="o">=</span> <span class="nx">require</span> <span class="s">&#39;coffeescript-module&#39;</span>

<span class="nv">File      = </span><span class="nx">require</span> <span class="s">&#39;./psd/file.coffee&#39;</span>
<span class="nv">LazyExecute = </span><span class="nx">require</span> <span class="s">&#39;./psd/lazy_execute.coffee&#39;</span>

<span class="nv">Header    = </span><span class="nx">require</span> <span class="s">&#39;./psd/header.coffee&#39;</span>
<span class="nv">Resources = </span><span class="nx">require</span> <span class="s">&#39;./psd/resources.coffee&#39;</span>
<span class="nv">LayerMask = </span><span class="nx">require</span> <span class="s">&#39;./psd/layer_mask.coffee&#39;</span>
<span class="nv">Image     = </span><span class="nx">require</span> <span class="s">&#39;./psd/image.coffee&#39;</span>

<span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">PSD</span> <span class="k">extends</span> <span class="nx">Module</span>
  <span class="vi">@Node:</span>
    <span class="nv">Root: </span><span class="nx">require</span><span class="p">(</span><span class="s">&#39;./psd/nodes/root.coffee&#39;</span><span class="p">)</span>

  <span class="nx">@</span><span class="k">extends</span> <span class="nx">require</span><span class="p">(</span><span class="s">&#39;./psd/init.coffee&#39;</span><span class="p">)</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>Creates a new PSD object. Typically you will use a helper method to instantiate
the PSD object. However, if you already have the PSD data stored as a Uint8Array,
you can instantiate the PSD object directly.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">constructor: </span><span class="nf">(data) -&gt;</span>
    <span class="vi">@file = </span><span class="k">new</span> <span class="nx">File</span><span class="p">(</span><span class="nx">data</span><span class="p">)</span>
    <span class="vi">@parsed = </span><span class="kc">false</span>
    <span class="vi">@header = </span><span class="kc">null</span>

    <span class="nb">Object</span><span class="p">.</span><span class="nx">defineProperty</span> <span class="nx">@</span><span class="p">,</span> <span class="s">&#39;layers&#39;</span><span class="p">,</span>
      <span class="nv">get: </span><span class="nf">-&gt;</span> <span class="nx">@layerMask</span><span class="p">.</span><span class="nx">layers</span>

    <span class="nx">RSVP</span><span class="p">.</span><span class="nx">on</span> <span class="s">&#39;error&#39;</span><span class="p">,</span> <span class="nf">(reason) -&gt;</span> <span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="nx">reason</span><span class="p">)</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>Parses the PSD. You must call this method before attempting to
access PSD data. It will not re-parse the PSD if it has already
been parsed.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parse: </span><span class="nf">-&gt;</span>
    <span class="k">return</span> <span class="k">if</span> <span class="nx">@parsed</span>

    <span class="nx">@parseHeader</span><span class="p">()</span>
    <span class="nx">@parseResources</span><span class="p">()</span>
    <span class="nx">@parseLayerMask</span><span class="p">()</span>
    <span class="nx">@parseImage</span><span class="p">()</span>

    <span class="vi">@parsed = </span><span class="kc">true</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>The next 4 methods are responsible for parsing the 4 main sections of the PSD.
These are private, and you should never call them from your own code.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parseHeader: </span><span class="nf">-&gt;</span>
    <span class="vi">@header = </span><span class="k">new</span> <span class="nx">Header</span><span class="p">(</span><span class="nx">@file</span><span class="p">)</span>
    <span class="nx">@header</span><span class="p">.</span><span class="nx">parse</span><span class="p">()</span>

  <span class="nv">parseResources: </span><span class="nf">-&gt;</span>
    <span class="nv">resources = </span><span class="k">new</span> <span class="nx">Resources</span><span class="p">(</span><span class="nx">@file</span><span class="p">)</span>
    <span class="vi">@resources = </span><span class="k">new</span> <span class="nx">LazyExecute</span><span class="p">(</span><span class="nx">resources</span><span class="p">,</span> <span class="nx">@file</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">now</span><span class="p">(</span><span class="s">&#39;skip&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">later</span><span class="p">(</span><span class="s">&#39;parse&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">get</span><span class="p">()</span>

  <span class="nv">parseLayerMask: </span><span class="nf">-&gt;</span>
    <span class="nv">layerMask = </span><span class="k">new</span> <span class="nx">LayerMask</span><span class="p">(</span><span class="nx">@file</span><span class="p">,</span> <span class="nx">@header</span><span class="p">)</span>
    <span class="vi">@layerMask = </span><span class="k">new</span> <span class="nx">LazyExecute</span><span class="p">(</span><span class="nx">layerMask</span><span class="p">,</span> <span class="nx">@file</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">now</span><span class="p">(</span><span class="s">&#39;skip&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">later</span><span class="p">(</span><span class="s">&#39;parse&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">get</span><span class="p">()</span>

  <span class="nv">parseImage: </span><span class="nf">-&gt;</span>
    <span class="nv">image = </span><span class="k">new</span> <span class="nx">Image</span><span class="p">(</span><span class="nx">@file</span><span class="p">,</span> <span class="nx">@header</span><span class="p">)</span>
    <span class="vi">@image = </span><span class="k">new</span> <span class="nx">LazyExecute</span><span class="p">(</span><span class="nx">image</span><span class="p">,</span> <span class="nx">@file</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">later</span><span class="p">(</span><span class="s">&#39;parse&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">ignore</span><span class="p">(</span><span class="s">&#39;width&#39;</span><span class="p">,</span> <span class="s">&#39;height&#39;</span><span class="p">)</span>
      <span class="p">.</span><span class="nx">get</span><span class="p">()</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>Returns a tree representation of the PSD document, which is the
preferred way of accessing most of the PSD&#39;s data.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">tree: </span><span class="nf">-&gt;</span> <span class="k">new</span> <span class="nx">PSD</span><span class="p">.</span><span class="nx">Node</span><span class="p">.</span><span class="nx">Root</span><span class="p">(</span><span class="nx">@</span><span class="p">)</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:15 GMT-0400 (EDT)  </div></div></body></html>
<!DOCTYPE html><html><head><title>file.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../index.html" class="source"><span class="file_name">README</span></a><a href="../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../lib/psd/file.coffee.html" class="source selected"><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>file.coffee</h1><div class="filepath">lib/psd/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="p">{</span><span class="nx">jspack</span><span class="p">}</span> <span class="o">=</span> <span class="nx">require</span> <span class="s">&#39;jspack&#39;</span>
<span class="nv">iconv = </span><span class="nx">require</span> <span class="s">&#39;iconv-lite&#39;</span>
<span class="nv">Color = </span><span class="nx">require</span> <span class="s">&#39;./color.coffee&#39;</span>
<span class="nv">Util = </span><span class="nx">require</span> <span class="s">&#39;./util.coffee&#39;</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>A file abstraction that stores the PSD file data, and
assists in parsing it.</p>

</td><td class="code"><div class="highlight"><pre><span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">File</span>
  <span class="nv">FORMATS =</span>
    <span class="nv">Int:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;i&#39;</span>
      <span class="nv">length: </span><span class="mi">4</span>
    <span class="nv">UInt:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;I&#39;</span>
      <span class="nv">length: </span><span class="mi">4</span>
    <span class="nv">Short:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;h&#39;</span>
      <span class="nv">length: </span><span class="mi">2</span>
    <span class="nv">UShort:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;H&#39;</span>
      <span class="nv">length: </span><span class="mi">2</span>
    <span class="nv">Float:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;f&#39;</span>
      <span class="nv">length: </span><span class="mi">4</span>
    <span class="nv">Double:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;d&#39;</span>
      <span class="nv">length: </span><span class="mi">8</span>
    <span class="nv">LongLong:</span>
      <span class="nv">code: </span><span class="s">&#39;&gt;q&#39;</span>
      <span class="nv">length: </span><span class="mi">8</span>

  <span class="k">for</span> <span class="k">own</span> <span class="nx">format</span><span class="p">,</span> <span class="nx">info</span> <span class="k">of</span> <span class="nx">FORMATS</span> <span class="k">then</span> <span class="nx">do</span> <span class="nf">(format, info) =&gt;</span>
    <span class="nx">@</span><span class="o">::</span><span class="p">[</span><span class="s">&quot;read</span><span class="si">#{</span><span class="nx">format</span><span class="si">}</span><span class="s">&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="nf">-&gt;</span> <span class="nx">@readf</span><span class="p">(</span><span class="nx">info</span><span class="p">.</span><span class="nx">code</span><span class="p">,</span> <span class="nx">info</span><span class="p">.</span><span class="nx">length</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>The current cursor position in the file.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">pos: </span><span class="mi">0</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>Creates a new File with the given Uint8Array.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">constructor: </span><span class="nf">(@data) -&gt;</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>Returns the current cursor position.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">tell: </span><span class="nf">-&gt;</span> <span class="nx">@pos</span></pre></div></td></tr><tr id="section-6"><td class="docs"><div class="pilwrap"><a href="#section-6" class="pilcrow">&#182;</a></div><p>Reads raw file data with no processing.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">read: </span><span class="nf">(length) -&gt;</span> <span class="p">(</span><span class="nx">@data</span><span class="p">[</span><span class="nx">@pos</span><span class="o">++</span><span class="p">]</span> <span class="k">for</span> <span class="nx">i</span> <span class="k">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">...</span><span class="nx">length</span><span class="p">])</span></pre></div></td></tr><tr id="section-7"><td class="docs"><div class="pilwrap"><a href="#section-7" class="pilcrow">&#182;</a></div><p>Reads file data and processes it with the given unpack format string. If the length is
omitted, then it will be calculated automatically based on the format string.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readf: </span><span class="nf">(format, len = null) -&gt;</span> <span class="nx">jspack</span><span class="p">.</span><span class="nx">Unpack</span> <span class="nx">format</span><span class="p">,</span> <span class="nx">@read</span><span class="p">(</span><span class="nx">len</span> <span class="o">or</span> <span class="nx">jspack</span><span class="p">.</span><span class="nx">CalcLength</span><span class="p">(</span><span class="nx">format</span><span class="p">))</span></pre></div></td></tr><tr id="section-8"><td class="docs"><div class="pilwrap"><a href="#section-8" class="pilcrow">&#182;</a></div><p>Moves the cursor without parsing data. If <code>rel = false</code>, then the cursor will be set to the
given value, which effectively sets the position relative to the start of the file. If
<code>rel = true</code>, then the cursor will be moved relative to the current position.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">seek: </span><span class="nf">(amt, rel = false) -&gt;</span> <span class="k">if</span> <span class="nx">rel</span> <span class="k">then</span> <span class="nx">@pos</span> <span class="o">+=</span> <span class="nx">amt</span> <span class="k">else</span> <span class="vi">@pos = </span><span class="nx">amt</span></pre></div></td></tr><tr id="section-9"><td class="docs"><div class="pilwrap"><a href="#section-9" class="pilcrow">&#182;</a></div><p>Reads a String of the given length.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readString: </span><span class="nf">(length) -&gt;</span> <span class="nb">String</span><span class="p">.</span><span class="nx">fromCharCode</span><span class="p">.</span><span class="nx">apply</span><span class="p">(</span><span class="kc">null</span><span class="p">,</span> <span class="nx">@read</span><span class="p">(</span><span class="nx">length</span><span class="p">)).</span><span class="nx">replace</span> <span class="sr">/\u0000/g</span><span class="p">,</span> <span class="s">&quot;&quot;</span></pre></div></td></tr><tr id="section-10"><td class="docs"><div class="pilwrap"><a href="#section-10" class="pilcrow">&#182;</a></div><p>Reads a Unicode UTF-16BE encoded string.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readUnicodeString: </span><span class="nf">(length = null) -&gt;</span>
    <span class="nx">length</span> <span class="o">or=</span> <span class="nx">@readInt</span><span class="p">()</span>
    <span class="nx">iconv</span><span class="p">.</span><span class="nx">decode</span><span class="p">(</span><span class="k">new</span> <span class="nx">Buffer</span><span class="p">(</span><span class="nx">@read</span><span class="p">(</span><span class="nx">length</span> <span class="o">*</span> <span class="mi">2</span><span class="p">)),</span><span class="s">&#39;utf-16be&#39;</span><span class="p">).</span><span class="nx">replace</span> <span class="sr">/\u0000/g</span><span class="p">,</span> <span class="s">&quot;&quot;</span></pre></div></td></tr><tr id="section-11"><td class="docs"><div class="pilwrap"><a href="#section-11" class="pilcrow">&#182;</a></div><p>Helper that reads a single byte.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readByte: </span><span class="nf">-&gt;</span> <span class="nx">@read</span><span class="p">(</span><span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span></pre></div></td></tr><tr id="section-12"><td class="docs"><div class="pilwrap"><a href="#section-12" class="pilcrow">&#182;</a></div><p>Helper that reads a single byte and interprets it as a boolean.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readBoolean: </span><span class="nf">-&gt;</span> <span class="nx">@readByte</span><span class="p">()</span> <span class="o">isnt</span> <span class="mi">0</span></pre></div></td></tr><tr id="section-13"><td class="docs"><div class="pilwrap"><a href="#section-13" class="pilcrow">&#182;</a></div><p>Reads a 32-bit color space value.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readSpaceColor: </span><span class="nf">-&gt;</span>
    <span class="nv">colorSpace = </span><span class="nx">@readShort</span><span class="p">()</span>
    <span class="nv">colorComponent = </span><span class="p">(</span><span class="nx">@readShort</span><span class="p">()</span> <span class="o">&gt;&gt;</span> <span class="mi">8</span><span class="p">)</span> <span class="k">for</span> <span class="nx">i</span> <span class="k">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">...</span><span class="mi">4</span><span class="p">]</span>

    <span class="nv">colorSpace: </span><span class="nx">colorSpace</span><span class="p">,</span> <span class="nv">components: </span><span class="nx">colorComponent</span></pre></div></td></tr><tr id="section-14"><td class="docs"><div class="pilwrap"><a href="#section-14" class="pilcrow">&#182;</a></div><p>Adobe&#39;s lovely signed 32-bit fixed-point number with 8bits.24bits
  <a href="http://www.adobe.com/devnet-apps/photoshop/fileformatashtml/PhotoshopFileFormats.htm#50577409_17587">http://www.adobe.com/devnet-apps/photoshop/fileformatashtml/PhotoshopFileFormats.htm#50577409_17587</a></p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">readPathNumber: </span><span class="nf">-&gt;</span>
    <span class="nv">a = </span><span class="nx">@readByte</span><span class="p">()</span>
    
    <span class="nv">arr = </span><span class="nx">@read</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
    <span class="nv">b1 = </span><span class="nx">arr</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">&lt;&lt;</span> <span class="mi">16</span>
    <span class="nv">b2 = </span><span class="nx">arr</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">&lt;&lt;</span> <span class="mi">8</span>
    <span class="nv">b3 = </span><span class="nx">arr</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
    <span class="nv">b = </span><span class="nx">b1</span> <span class="o">|</span> <span class="nx">b2</span> <span class="o">|</span> <span class="nx">b3</span>

    <span class="nb">parseFloat</span><span class="p">(</span><span class="nx">a</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span> <span class="o">+</span> <span class="nb">parseFloat</span><span class="p">(</span><span class="nx">b</span> <span class="o">/</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">pow</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">24</span><span class="p">),</span> <span class="mi">10</span><span class="p">)</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:10 GMT-0400 (EDT)  </div></div></body></html>
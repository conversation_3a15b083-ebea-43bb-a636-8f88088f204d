<!DOCTYPE html><html><head><title>typetool.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../../index.html" class="source"><span class="file_name">README</span></a><a href="../../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../../lib/psd/header.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../../lib/psd/layer_info/typetool.coffee.html" class="source selected"><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>typetool.coffee</h1><div class="filepath">lib/psd/layer_info/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="nv">_ = </span><span class="nx">require</span> <span class="s">&#39;lodash&#39;</span>
<span class="nv">parseEngineData = </span><span class="nx">require</span> <span class="s">&#39;parse-engine-data&#39;</span>
<span class="nv">LayerInfo = </span><span class="nx">require</span> <span class="s">&#39;../layer_info.coffee&#39;</span>
<span class="nv">Descriptor = </span><span class="nx">require</span> <span class="s">&#39;../descriptor.coffee&#39;</span>

<span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">TextElements</span> <span class="k">extends</span> <span class="nx">LayerInfo</span>
  <span class="vi">@shouldParse: </span><span class="nf">(key) -&gt;</span> <span class="nx">key</span> <span class="o">is</span> <span class="s">&#39;TySh&#39;</span>

  <span class="nv">TRANSFORM_VALUE = </span><span class="p">[</span><span class="s">&#39;xx&#39;</span><span class="p">,</span> <span class="s">&#39;xy&#39;</span><span class="p">,</span> <span class="s">&#39;yx&#39;</span><span class="p">,</span> <span class="s">&#39;yy&#39;</span><span class="p">,</span> <span class="s">&#39;tx&#39;</span><span class="p">,</span> <span class="s">&#39;ty&#39;</span><span class="p">]</span>
  <span class="nv">COORDS_VALUE = </span><span class="p">[</span><span class="s">&#39;left&#39;</span><span class="p">,</span> <span class="s">&#39;top&#39;</span><span class="p">,</span> <span class="s">&#39;right&#39;</span><span class="p">,</span> <span class="s">&#39;bottom&#39;</span><span class="p">]</span>

  <span class="nv">constructor: </span><span class="nf">(layer, length) -&gt;</span>
    <span class="k">super</span><span class="p">(</span><span class="nx">layer</span><span class="p">,</span> <span class="nx">length</span><span class="p">)</span>

    <span class="vi">@version = </span><span class="kc">null</span>
    <span class="vi">@transform = </span><span class="p">{}</span>
    <span class="vi">@textVersion = </span><span class="kc">null</span>
    <span class="vi">@descriptorVersion = </span><span class="kc">null</span>
    <span class="vi">@textData = </span><span class="kc">null</span>
    <span class="vi">@engineData = </span><span class="kc">null</span>
    <span class="vi">@textValue = </span><span class="kc">null</span>
    <span class="vi">@warpVersion = </span><span class="kc">null</span>
    <span class="vi">@descriptorVersion = </span><span class="kc">null</span>
    <span class="vi">@warpData = </span><span class="kc">null</span>
    <span class="vi">@coords = </span><span class="p">{}</span>

  <span class="nv">parse: </span><span class="nf">-&gt;</span>
    <span class="vi">@version = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readShort</span><span class="p">()</span>

    <span class="nx">@parseTransformInfo</span><span class="p">()</span>

    <span class="vi">@textVersion = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readShort</span><span class="p">()</span>
    <span class="vi">@descriptorVersion = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>

    <span class="vi">@textData = </span><span class="k">new</span> <span class="nx">Descriptor</span><span class="p">(</span><span class="nx">@file</span><span class="p">).</span><span class="nx">parse</span><span class="p">()</span>
    <span class="vi">@textValue = </span><span class="nx">@textData</span><span class="p">[</span><span class="s">&#39;Txt &#39;</span><span class="p">]</span>
    <span class="vi">@engineData = </span><span class="nx">parseEngineData</span><span class="p">(</span><span class="nx">@textData</span><span class="p">.</span><span class="nx">EngineData</span><span class="p">)</span>

    <span class="vi">@warpVersion = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readShort</span><span class="p">()</span>

    <span class="vi">@descriptorVersion = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>

    <span class="vi">@warpData = </span><span class="k">new</span> <span class="nx">Descriptor</span><span class="p">(</span><span class="nx">@file</span><span class="p">).</span><span class="nx">parse</span><span class="p">()</span>

    <span class="k">for</span> <span class="nx">name</span><span class="p">,</span> <span class="nx">index</span> <span class="k">in</span> <span class="nx">COORDS_VALUE</span>
      <span class="nx">@coords</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span> <span class="o">=</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readInt</span><span class="p">()</span>

  <span class="nv">parseTransformInfo: </span><span class="nf">-&gt;</span>
    <span class="k">for</span> <span class="nx">name</span><span class="p">,</span> <span class="nx">index</span> <span class="k">in</span> <span class="nx">TRANSFORM_VALUE</span>
      <span class="nx">@transform</span><span class="p">[</span><span class="nx">name</span><span class="p">]</span> <span class="o">=</span> <span class="nx">@file</span><span class="p">.</span><span class="nx">readDouble</span><span class="p">()</span>

  <span class="nv">fonts: </span><span class="nf">-&gt;</span>
    <span class="k">return</span> <span class="p">[]</span> <span class="k">unless</span> <span class="nx">@engineData</span><span class="o">?</span>
    <span class="nx">@engineData</span><span class="p">.</span><span class="nx">ResourceDict</span><span class="p">.</span><span class="nx">FontSet</span><span class="p">.</span><span class="nx">map</span> <span class="nf">(f) -&gt;</span> <span class="nx">f</span><span class="p">.</span><span class="nx">Name</span>

  <span class="nv">sizes: </span><span class="nf">-&gt;</span>
    <span class="k">return</span> <span class="p">[]</span> <span class="k">if</span> <span class="o">not</span> <span class="nx">@engineData</span><span class="o">?</span> <span class="o">and</span> <span class="o">not</span> <span class="nx">@styles</span><span class="p">().</span><span class="nx">FontSize</span><span class="o">?</span>
    <span class="nx">@styles</span><span class="p">().</span><span class="nx">FontSize</span>

  <span class="nv">alignment: </span><span class="nf">-&gt;</span>
    <span class="k">return</span> <span class="p">[]</span> <span class="k">unless</span> <span class="nx">@engineData</span><span class="o">?</span>
    <span class="nv">alignments = </span><span class="p">[</span><span class="s">&#39;left&#39;</span><span class="p">,</span> <span class="s">&#39;right&#39;</span><span class="p">,</span> <span class="s">&#39;center&#39;</span><span class="p">,</span> <span class="s">&#39;justify&#39;</span><span class="p">]</span>
    <span class="nx">@engineData</span><span class="p">.</span><span class="nx">EngineDict</span><span class="p">.</span><span class="nx">ParagraphRun</span><span class="p">.</span><span class="nx">RunArray</span><span class="p">.</span><span class="nx">map</span> <span class="nf">(s) -&gt;</span>
      <span class="nx">alignments</span><span class="p">[</span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="nb">parseInt</span><span class="p">(</span><span class="nx">s</span><span class="p">.</span><span class="nx">ParagraphSheet</span><span class="p">.</span><span class="nx">Properties</span><span class="p">.</span><span class="nx">Justification</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="mi">3</span><span class="p">)]</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>Return all colors used for text in this layer. The colors are returned in RGBA
format as an array of arrays.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">colors: </span><span class="nf">-&gt;</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>If the color is opaque black, this field is sometimes omitted.</p>

</td><td class="code"><div class="highlight"><pre>    <span class="k">return</span> <span class="p">[[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">]]</span> <span class="k">if</span> <span class="o">not</span> <span class="nx">@engineData</span><span class="o">?</span> <span class="o">or</span> <span class="o">not</span> <span class="nx">@styles</span><span class="p">().</span><span class="nx">FillColor</span><span class="o">?</span>

    <span class="nx">@styles</span><span class="p">().</span><span class="nx">FillColor</span><span class="p">.</span><span class="nx">map</span> <span class="nf">(s) -&gt;</span>
      <span class="nv">values = </span><span class="nx">s</span><span class="p">.</span><span class="nx">Values</span><span class="p">.</span><span class="nx">map</span> <span class="nf">(v) -&gt;</span> <span class="nb">Math</span><span class="p">.</span><span class="nx">round</span><span class="p">(</span><span class="nx">v</span> <span class="o">*</span> <span class="mi">255</span><span class="p">)</span>
      <span class="nx">values</span><span class="p">.</span><span class="nx">push</span> <span class="nx">values</span><span class="p">.</span><span class="nx">shift</span><span class="p">()</span> <span class="c1"># Change ARGB -&gt; RGBA for consistency</span>
      <span class="nx">values</span>

  <span class="nv">styles: </span><span class="nf">-&gt;</span>
    <span class="k">return</span> <span class="p">{}</span> <span class="k">unless</span> <span class="nx">@engineData</span><span class="o">?</span>
    <span class="k">return</span> <span class="nx">@_styles</span> <span class="k">if</span> <span class="nx">@_styles</span><span class="o">?</span>

    <span class="nv">data = </span><span class="nx">@engineData</span><span class="p">.</span><span class="nx">EngineDict</span><span class="p">.</span><span class="nx">StyleRun</span><span class="p">.</span><span class="nx">RunArray</span><span class="p">.</span><span class="nx">map</span> <span class="nf">(r) -&gt;</span>
      <span class="nx">r</span><span class="p">.</span><span class="nx">StyleSheet</span><span class="p">.</span><span class="nx">StyleSheetData</span>

    <span class="vi">@_styles = </span><span class="nx">_</span><span class="p">.</span><span class="nx">reduce</span><span class="p">(</span><span class="nx">data</span><span class="p">,</span> <span class="nf">(m, o) -&gt;</span>
      <span class="k">for</span> <span class="k">own</span> <span class="nx">k</span><span class="p">,</span> <span class="nx">v</span> <span class="k">of</span> <span class="nx">o</span>
        <span class="nx">m</span><span class="p">[</span><span class="nx">k</span><span class="p">]</span> <span class="o">or=</span> <span class="p">[]</span>
        <span class="nx">m</span><span class="p">[</span><span class="nx">k</span><span class="p">].</span><span class="nx">push</span> <span class="nx">v</span>
      <span class="nx">m</span>
    <span class="p">,</span> <span class="p">{})</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>Creates the CSS string and returns it. Each property is newline separated
and not all properties may be present depending on the document.</p>
<p>Colors are returned in rgba() format and fonts may include some internal
Photoshop fonts.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">toCSS: </span><span class="nf">-&gt;</span>
    <span class="nv">definition =</span>
      <span class="s">&#39;font-family&#39;</span><span class="o">:</span> <span class="nx">@fonts</span><span class="p">().</span><span class="nx">join</span><span class="p">(</span><span class="s">&#39;, &#39;</span><span class="p">)</span>
      <span class="s">&#39;font-size&#39;</span><span class="o">:</span> <span class="s">&quot;</span><span class="si">#{</span><span class="nx">@sizes</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span><span class="si">}</span><span class="s">pt&quot;</span>
      <span class="s">&#39;color&#39;</span><span class="o">:</span> <span class="s">&quot;rgba(</span><span class="si">#{</span><span class="nx">@colors</span><span class="p">()[</span><span class="mi">0</span><span class="p">].</span><span class="nx">join</span><span class="p">(</span><span class="s">&#39;, &#39;</span><span class="p">)</span><span class="si">}</span><span class="s">)&quot;</span>
      <span class="s">&#39;text-align&#39;</span><span class="o">:</span> <span class="nx">@alignment</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span>

    <span class="nv">css = </span><span class="p">[]</span>
    <span class="k">for</span> <span class="nx">k</span><span class="p">,</span> <span class="nx">v</span> <span class="k">of</span> <span class="nx">definition</span>
      <span class="k">continue</span> <span class="k">unless</span> <span class="nx">v</span><span class="o">?</span>
      <span class="nx">css</span><span class="p">.</span><span class="nx">push</span> <span class="s">&quot;</span><span class="si">#{</span><span class="nx">k</span><span class="si">}</span><span class="s">: </span><span class="si">#{</span><span class="nx">v</span><span class="si">}</span><span class="s">;&quot;</span>

    <span class="nx">css</span><span class="p">.</span><span class="nx">join</span><span class="p">(</span><span class="s">&quot;\n&quot;</span><span class="p">)</span>

  <span class="nv">export: </span><span class="nf">-&gt;</span>
    <span class="nv">value: </span><span class="nx">@textValue</span>
    <span class="nv">font:</span>
      <span class="nv">name: </span><span class="nx">@fonts</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span>
      <span class="nv">sizes: </span><span class="nx">@sizes</span><span class="p">()</span>
      <span class="nv">colors: </span><span class="nx">@colors</span><span class="p">()</span>
      <span class="nv">alignment: </span><span class="nx">@alignment</span><span class="p">()</span>
    <span class="nv">left: </span><span class="nx">@coords</span><span class="p">.</span><span class="nx">left</span>
    <span class="nv">top: </span><span class="nx">@coords</span><span class="p">.</span><span class="nx">top</span>
    <span class="nv">right: </span><span class="nx">@coords</span><span class="p">.</span><span class="nx">right</span>
    <span class="nv">bottom: </span><span class="nx">@coords</span><span class="p">.</span><span class="nx">bottom</span>
    <span class="nv">transform: </span><span class="nx">@transform</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:13 GMT-0400 (EDT)  </div></div></body></html>
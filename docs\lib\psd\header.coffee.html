<!DOCTYPE html><html><head><title>header.coffee</title><meta http-equiv="Content-Type" content="text/html" charset="UTF-8"><link rel="stylesheet" media="all" href="../../docco.css"></head><body><div id="container"><div id="background"></div><div id="jump_to">Jump To &hellip;<div id="jump_wrapper"><div id="jump_page"><a href="../../index.html" class="source"><span class="file_name">README</span></a><a href="../../lib/psd/blend_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">blend_mode.coffee</span></a><a href="../../lib/psd/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/color.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">color.coffee</span></a><a href="../../lib/psd/descriptor.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">descriptor.coffee</span></a><a href="../../lib/psd/file.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">file.coffee</span></a><a href="../../lib/psd/header.coffee.html" class="source selected"><span class="base_path">lib / psd / </span><span class="file_name">header.coffee</span></a><a href="../../lib/psd/image.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image.coffee</span></a><a href="../../lib/psd/image_export.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_export.coffee</span></a><a href="../../lib/psd/image_exports/png.coffee.html" class="source "><span class="base_path">lib / psd / image_exports / </span><span class="file_name">png.coffee</span></a><a href="../../lib/psd/image_format.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_format.coffee</span></a><a href="../../lib/psd/image_formats/layer_raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_raw.coffee</span></a><a href="../../lib/psd/image_formats/layer_rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">layer_rle.coffee</span></a><a href="../../lib/psd/image_formats/raw.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">raw.coffee</span></a><a href="../../lib/psd/image_formats/rle.coffee.html" class="source "><span class="base_path">lib / psd / image_formats / </span><span class="file_name">rle.coffee</span></a><a href="../../lib/psd/image_mode.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">image_mode.coffee</span></a><a href="../../lib/psd/image_modes/cmyk.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">cmyk.coffee</span></a><a href="../../lib/psd/image_modes/greyscale.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">greyscale.coffee</span></a><a href="../../lib/psd/image_modes/rgb.coffee.html" class="source "><span class="base_path">lib / psd / image_modes / </span><span class="file_name">rgb.coffee</span></a><a href="../../lib/psd/init.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">init.coffee</span></a><a href="../../lib/psd/layer/blend_modes.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blend_modes.coffee</span></a><a href="../../lib/psd/layer/blending_ranges.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">blending_ranges.coffee</span></a><a href="../../lib/psd/layer/channel_image.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">channel_image.coffee</span></a><a href="../../lib/psd/layer/helpers.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">helpers.coffee</span></a><a href="../../lib/psd/layer/info.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">info.coffee</span></a><a href="../../lib/psd/layer/mask.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/layer/name.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">name.coffee</span></a><a href="../../lib/psd/layer/position_channels.coffee.html" class="source "><span class="base_path">lib / psd / layer / </span><span class="file_name">position_channels.coffee</span></a><a href="../../lib/psd/layer.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/layer_info/blend_clipping_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_clipping_elements.coffee</span></a><a href="../../lib/psd/layer_info/blend_interior_elements.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">blend_interior_elements.coffee</span></a><a href="../../lib/psd/layer_info/fill_opacity.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">fill_opacity.coffee</span></a><a href="../../lib/psd/layer_info/gradient_fill.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">gradient_fill.coffee</span></a><a href="../../lib/psd/layer_info/layer_id.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_id.coffee</span></a><a href="../../lib/psd/layer_info/layer_name_source.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">layer_name_source.coffee</span></a><a href="../../lib/psd/layer_info/legacy_typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">legacy_typetool.coffee</span></a><a href="../../lib/psd/layer_info/locked.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">locked.coffee</span></a><a href="../../lib/psd/layer_info/metadata.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">metadata.coffee</span></a><a href="../../lib/psd/layer_info/nested_section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">nested_section_divider.coffee</span></a><a href="../../lib/psd/layer_info/object_effects.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">object_effects.coffee</span></a><a href="../../lib/psd/layer_info/section_divider.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">section_divider.coffee</span></a><a href="../../lib/psd/layer_info/solid_color.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">solid_color.coffee</span></a><a href="../../lib/psd/layer_info/typetool.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">typetool.coffee</span></a><a href="../../lib/psd/layer_info/unicode_name.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">unicode_name.coffee</span></a><a href="../../lib/psd/layer_info/vector_mask.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_mask.coffee</span></a><a href="../../lib/psd/layer_info/vector_origination.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_origination.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke.coffee</span></a><a href="../../lib/psd/layer_info/vector_stroke_content.coffee.html" class="source "><span class="base_path">lib / psd / layer_info / </span><span class="file_name">vector_stroke_content.coffee</span></a><a href="../../lib/psd/layer_info.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_info.coffee</span></a><a href="../../lib/psd/layer_mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">layer_mask.coffee</span></a><a href="../../lib/psd/lazy_execute.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">lazy_execute.coffee</span></a><a href="../../lib/psd/mask.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">mask.coffee</span></a><a href="../../lib/psd/node.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">node.coffee</span></a><a href="../../lib/psd/nodes/ancestry.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">ancestry.coffee</span></a><a href="../../lib/psd/nodes/build_preview.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">build_preview.coffee</span></a><a href="../../lib/psd/nodes/group.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">group.coffee</span></a><a href="../../lib/psd/nodes/layer.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">layer.coffee</span></a><a href="../../lib/psd/nodes/root.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">root.coffee</span></a><a href="../../lib/psd/nodes/search.coffee.html" class="source "><span class="base_path">lib / psd / nodes / </span><span class="file_name">search.coffee</span></a><a href="../../lib/psd/path_record.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">path_record.coffee</span></a><a href="../../lib/psd/resource.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource.coffee</span></a><a href="../../lib/psd/resource_section.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resource_section.coffee</span></a><a href="../../lib/psd/resources/layer_comps.coffee.html" class="source "><span class="base_path">lib / psd / resources / </span><span class="file_name">layer_comps.coffee</span></a><a href="../../lib/psd/resources.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">resources.coffee</span></a><a href="../../lib/psd/util.coffee.html" class="source "><span class="base_path">lib / psd / </span><span class="file_name">util.coffee</span></a><a href="../../lib/psd.coffee.html" class="source "><span class="base_path">lib / </span><span class="file_name">psd.coffee</span></a><a href="../../shims/init.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">init.coffee</span></a><a href="../../shims/png.coffee.html" class="source "><span class="base_path">shims / </span><span class="file_name">png.coffee</span></a></div></div></div><table cellpadding="0" cellspacing="0"><thead><tr><th class="docs"><h1>header.coffee</h1><div class="filepath">lib/psd/</div></th><th class="code"></th></tr></thead><tbody><tr id="section-1"><td class="docs"><div class="pilwrap"><a href="#section-1" class="pilcrow">&#182;</a></div>
</td><td class="code"><div class="highlight"><pre><span class="p">{</span><span class="nx">Module</span><span class="p">}</span> <span class="o">=</span> <span class="nx">require</span> <span class="s">&#39;coffeescript-module&#39;</span></pre></div></td></tr><tr id="section-2"><td class="docs"><div class="pilwrap"><a href="#section-2" class="pilcrow">&#182;</a></div><p>Represents the header of the PSD, which is the first thing always parsed.
The header stores important information about the PSD such as the dimensions
and the color depth.</p>

</td><td class="code"><div class="highlight"><pre><span class="nv">module.exports = </span><span class="k">class</span> <span class="nx">Header</span> <span class="k">extends</span> <span class="nx">Module</span>
  <span class="nx">@aliasProperty</span> <span class="s">&#39;height&#39;</span><span class="p">,</span> <span class="s">&#39;rows&#39;</span>
  <span class="nx">@aliasProperty</span> <span class="s">&#39;width&#39;</span><span class="p">,</span> <span class="s">&#39;cols&#39;</span></pre></div></td></tr><tr id="section-3"><td class="docs"><div class="pilwrap"><a href="#section-3" class="pilcrow">&#182;</a></div><p>All of the color modes are stored internally as a short from 0-15.
This is a mapping of that value to a human-readable name.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">MODES = </span><span class="p">[</span>
    <span class="s">&#39;Bitmap&#39;</span>
    <span class="s">&#39;GrayScale&#39;</span>
    <span class="s">&#39;IndexedColor&#39;</span>
    <span class="s">&#39;RGBColor&#39;</span>
    <span class="s">&#39;CMYKColor&#39;</span>
    <span class="s">&#39;HSLColor&#39;</span>
    <span class="s">&#39;HSBColor&#39;</span>
    <span class="s">&#39;Multichannel&#39;</span>
    <span class="s">&#39;Duotone&#39;</span>
    <span class="s">&#39;LabColor&#39;</span>
    <span class="s">&#39;Gray16&#39;</span>
    <span class="s">&#39;RGB48&#39;</span>
    <span class="s">&#39;Lab48&#39;</span>
    <span class="s">&#39;CMYK64&#39;</span>
    <span class="s">&#39;DeepMultichannel&#39;</span>
    <span class="s">&#39;Duotone16&#39;</span>
  <span class="p">]</span></pre></div></td></tr><tr id="section-4"><td class="docs"><div class="pilwrap"><a href="#section-4" class="pilcrow">&#182;</a></div><p>The signature of the PSD. Should be 8BPS.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">sig: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-5"><td class="docs"><div class="pilwrap"><a href="#section-5" class="pilcrow">&#182;</a></div><p>The version of the PSD. Should be 1.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">version: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-6"><td class="docs"><div class="pilwrap"><a href="#section-6" class="pilcrow">&#182;</a></div><p>The number of color channels in the PSD.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">channels: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-7"><td class="docs"><div class="pilwrap"><a href="#section-7" class="pilcrow">&#182;</a></div><p>The height of the PSD. Can also be accessed with <code>height</code>.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">rows: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-8"><td class="docs"><div class="pilwrap"><a href="#section-8" class="pilcrow">&#182;</a></div><p>The width of the PSD. Can also be accessed with <code>width</code>.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">cols: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-9"><td class="docs"><div class="pilwrap"><a href="#section-9" class="pilcrow">&#182;</a></div><p>The bit depth of the PSD.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">depth: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-10"><td class="docs"><div class="pilwrap"><a href="#section-10" class="pilcrow">&#182;</a></div><p>The color mode of the PSD.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">mode: </span><span class="kc">null</span></pre></div></td></tr><tr id="section-11"><td class="docs"><div class="pilwrap"><a href="#section-11" class="pilcrow">&#182;</a></div><p>Creates a new Header.
@param [File] file The PSD file.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">constructor: </span><span class="nf">(@file) -&gt;</span></pre></div></td></tr><tr id="section-12"><td class="docs"><div class="pilwrap"><a href="#section-12" class="pilcrow">&#182;</a></div><p>Parses the header data.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">parse: </span><span class="nf">-&gt;</span>
    <span class="vi">@sig = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readString</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
    <span class="vi">@version = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUShort</span><span class="p">()</span>

    <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="mi">6</span><span class="p">,</span> <span class="kc">true</span>

    <span class="vi">@channels = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUShort</span><span class="p">()</span>
    <span class="vi">@rows = @height = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUInt</span><span class="p">()</span>
    <span class="vi">@cols = @width = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUInt</span><span class="p">()</span>
    <span class="vi">@depth = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUShort</span><span class="p">()</span>
    <span class="vi">@mode = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUShort</span><span class="p">()</span>

    <span class="nv">colorDataLen = </span><span class="nx">@file</span><span class="p">.</span><span class="nx">readUInt</span><span class="p">()</span>
    <span class="nx">@file</span><span class="p">.</span><span class="nx">seek</span> <span class="nx">colorDataLen</span><span class="p">,</span> <span class="kc">true</span></pre></div></td></tr><tr id="section-13"><td class="docs"><div class="pilwrap"><a href="#section-13" class="pilcrow">&#182;</a></div><p>Converts the color mode key to a readable version.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">modeName: </span><span class="nf">-&gt;</span> <span class="nx">MODES</span><span class="p">[</span><span class="nx">@mode</span><span class="p">]</span></pre></div></td></tr><tr id="section-14"><td class="docs"><div class="pilwrap"><a href="#section-14" class="pilcrow">&#182;</a></div><p>Exports all of the header data in a basic object.</p>

</td><td class="code"><div class="highlight"><pre>  <span class="nv">export: </span><span class="nf">-&gt;</span>
    <span class="nv">data = </span><span class="p">{}</span>
    <span class="k">for</span> <span class="nx">key</span> <span class="k">in</span> <span class="p">[</span><span class="s">&#39;sig&#39;</span><span class="p">,</span> <span class="s">&#39;version&#39;</span><span class="p">,</span> <span class="s">&#39;channels&#39;</span><span class="p">,</span> <span class="s">&#39;rows&#39;</span><span class="p">,</span> <span class="s">&#39;cols&#39;</span><span class="p">,</span> <span class="s">&#39;depth&#39;</span><span class="p">,</span> <span class="s">&#39;mode&#39;</span><span class="p">]</span>
      <span class="nx">data</span><span class="p">[</span><span class="nx">key</span><span class="p">]</span> <span class="o">=</span> <span class="nx">@</span><span class="p">[</span><span class="nx">key</span><span class="p">]</span>

    <span class="nx">data</span>

</pre></div></td></tr></tbody></table><div id="generated">generated Tue May 12 2015 11:08:10 GMT-0400 (EDT)  </div></div></body></html>